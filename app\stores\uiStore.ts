import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import type { Notification, UIState } from "./types";

const initialState = {
  sidebarOpen: false,
  notifications: [],
};

export const useUIStore = create<UIState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        toggleSidebar: () => {
          set((state) => ({ sidebarOpen: !state.sidebarOpen }), false, "toggleSidebar");
        },

        setSidebarOpen: (open) => {
          set({ sidebarOpen: open }, false, "setSidebarOpen");
        },

        addNotification: (notification) => {
          // Generate UUID safely for both server and client
          let id: string;
          if (typeof crypto !== 'undefined' && crypto.randomUUID) {
            id = crypto.randomUUID();
          } else {
            // Fallback UUID generation
            id = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
              const r = Math.random() * 16 | 0;
              const v = c === 'x' ? r : (r & 0x3 | 0x8);
              return v.toString(16);
            });
          }

          const newNotification: Notification = {
            ...notification,
            id,
          };
          set(
            (state) => ({
              notifications: [...state.notifications, newNotification],
            }),
            false,
            "addNotification"
          );

          // Auto-remove notification if duration is set (only on client side)
          if (typeof window !== 'undefined' && notification.duration && notification.duration > 0) {
            setTimeout(() => {
              get().removeNotification(newNotification.id);
            }, notification.duration);
          }
        },

        removeNotification: (id) => {
          set(
            (state) => ({
              notifications: state.notifications.filter((n) => n.id !== id),
            }),
            false,
            "removeNotification"
          );
        },

        clearNotifications: () => {
          set({ notifications: [] }, false, "clearNotifications");
        },

        reset: () => {
          set(initialState, false, "reset");
        },
      }),
      {
        name: "ui-store",
        partialize: (state) => ({
          sidebarOpen: state.sidebarOpen,
        }),
        skipHydration: typeof window === "undefined",
      }
    ),
    {
      name: "ui-store",
    }
  )
);

// Selector functions
export const selectSidebarOpen = (state: UIState) => state.sidebarOpen;
export const selectNotifications = (state: UIState) => state.notifications;

// Convenience hooks
export const useSidebarOpen = () => useUIStore(selectSidebarOpen);
export const useNotifications = () => useUIStore(selectNotifications);

// Actions hooks
export const useUIActions = () => {
  const toggleSidebar = useUIStore((state) => state.toggleSidebar);
  const setSidebarOpen = useUIStore((state) => state.setSidebarOpen);
  const addNotification = useUIStore((state) => state.addNotification);
  const removeNotification = useUIStore((state) => state.removeNotification);
  const clearNotifications = useUIStore((state) => state.clearNotifications);
  const reset = useUIStore((state) => state.reset);

  return {
    toggleSidebar,
    setSidebarOpen,
    addNotification,
    removeNotification,
    clearNotifications,
    reset,
  };
};
